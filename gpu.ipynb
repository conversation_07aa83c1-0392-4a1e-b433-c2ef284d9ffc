import tensorflow as tf
from tensorflow import keras

(x_train,y_train),(x_test,y_test)=keras.datasets.mnist.load_data()
(x2_train, y2_train), (x2_test, y2_test) = keras.datasets.fashion_mnist.load_data()

import matplotlib.pyplot as plt
plt.imshow(x_test[0],cmap='gray')

x_train = x_train / 255.0
x_test = x_test / 255.0
x2_train=x2_train/255.0
x2_test=x2_test/255.0

def create_model(loss):
    model = keras.Sequential([
        keras.Input(shape=(28, 28)),
        keras.layers.Flatten(),

        keras.layers.Dense(100, activation='relu',use_bias=False),
        
        keras.layers.Dense(10, activation='softmax',use_bias=False)
        ])
        
    # Compile the model
    model.compile(optimizer='sgd',
                  loss=loss,
                  metrics=['accuracy'])
    return model

model = create_model('sparse_categorical_crossentropy')
model2 = create_model('sparse_categorical_crossentropy')
fmodel=create_model('sparse_categorical_crossentropy')
fmodel2=create_model('sparse_categorical_crossentropy')

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
g_data=pd.read_excel(r"/home/<USER>/Coding/MNIST ankit 2.xlsx")
g_data=(g_data['Conductance'].dropna())
g_data=g_data.iloc[10:400]

g_data

g_data.plot()
plt.show()

g_data.min()

def weight_scaler(model, g_data):
    weights = model.get_weights()
    g_max = g_data.max()
    g_min = g_data.min()
    new_weights = []
    for weight in weights:
        w_min = np.abs(weight).min()
        w_max = np.abs(weight).max()
        if w_max != w_min:
            weight = (g_max - g_min) * (weight - w_min) / (w_max - w_min) + g_min
        new_weights.append(weight)
    return new_weights

def train(model,x_train,y_train):
    model.fit(x_train,y_train,batch_size=32,epochs=1,verbose=1)
    

acc_model1=[]
acc_model2=[]
for j in range (100):
        print('Epoch number: ',j+1)
        loss,acc=model.evaluate(x_test,y_test,verbose=1)
        acc_model1.append(acc)
        model2.set_weights(weight_scaler(model,g_data))
        loss2,acc2=model2.evaluate(x_test,y_test,verbose=1)
        acc_model2.append(acc2)
        train(model,x_train,y_train)

plt.figure(figsize=(10, 6))
plt.plot(range(1, len(acc_model1) + 1), acc_model1, label='Software Model', marker='o', markersize=2)
plt.plot(range(1, len(acc_model2) + 1), acc_model2, label='Device Model', marker='o', markersize=2)

plt.xlabel('Training Steps', fontsize=12, fontfamily='Times New Roman', fontweight='bold')
plt.ylabel('Accuracy', fontsize=12, fontfamily='Times New Roman', fontweight='bold')
plt.title('Model Accuracy vs Training Steps', fontsize=14, fontfamily='Times New Roman', fontweight='bold')

plt.grid(True, linestyle='--', alpha=0.7)
plt.legend(frameon=False)
plt.ylim(0, 1.1)

# Save the data to CSV for future reference
acc_df = pd.DataFrame({
    'Step': range(1, len(acc_model1) + 1),
    'Software_Model': acc_model1,
    'Device_Model': acc_model2
})

plt.ylim(0.7,1)
acc_df.to_csv('accuracy_comparison.csv')

plt.tight_layout()
plt.show()

preds=model2.predict(x_test)
import seaborn as sns
from sklearn.metrics import confusion_matrix
from matplotlib import rcParams
import matplotlib.pyplot as plt
plt.figure(figsize=(10,10))
rcParams['font.family']='Times new roman'
rcParams['font.weight']='regular'
rcParams['font.size']=15
rcParams['axes.linewidth']=2

cm = confusion_matrix(y_test, preds.argmax(axis=1))
pd.DataFrame(cm).to_csv("Prediction heatmap data.csv")
sns.heatmap(cm, 
            annot=True, 
            fmt='d', 
            cmap='rocket', 
            cbar=False,
            linewidths=1,  # Add boundary lines between cells
            linecolor='black')  # Set boundary color to black
plt.gca().set_box_aspect(1)  # Make plot square
plt.gca().set_frame_on(True)  # Add outer frame

facc_model1=[]
facc_model2=[]
for j in range (20):
        print('Epoch number: ',j+1)
        loss,acc=fmodel.evaluate(x2_test,y2_test,verbose=1)
        facc_model1.append(acc)
        fmodel2.set_weights(weight_scaler(fmodel,g_data))
        loss2,acc2=fmodel2.evaluate(x2_test,y2_test,verbose=1)
        facc_model2.append(acc2)
        train(fmodel,x2_train,y2_train)

plt.rcParams.update({
    'font.size': 12,
    'font.family': 'Times new roman',
    'font.weight': 'bold',
})

row = {
    'Dataset': 2*['MNIST Digits', 'Fashion MNIST'],
    'accuracy': [acc_model1[19], facc_model1[19], acc_model2[19], facc_model2[19]],
    'Model': ['Software', 'Software', 'Device', 'Device']
}
report = pd.DataFrame(row)

plt.figure(figsize=(10.5, 7))  # Slightly increase the plot area
sns.barplot(data=report, x='Dataset', y='accuracy', hue='Model', palette='Blues', legend=True)
# Add value labels on top of each bar

[plt.gca().bar_label(i, fmt='%.2f', padding=3) for i in plt.gca().containers]

plt.xlabel('Dataset')  # Capitalize and bold labels
plt.ylabel('Accuracy')  # Capitalize and bold labels
report.to_csv('Digits versus fashion.csv')
plt.ylim(0,1.1)
plt.legend(loc='upper right',frameon=False)

(x_train, y_train), (x_test, y_test) = keras.datasets.fashion_mnist.load_data()

x_train = x_train / 255
x_test = x_test / 255

model = keras.models.Sequential()
model.add(keras.layers.Flatten())
model.add(keras.layers.Dense(128, activation='relu'))
model.add(keras.layers.Dense(10, activation='softmax'))
model.compile(optimizer='adam',
              loss='sparse_categorical_crossentropy',
              metrics=['accuracy'])
model.fit(x_train,y_train,batch_size=32,epochs=10)