import pandas as pd
import pandas as pd
data=pd.read_csv('/home/<USER>/Coding/Machine learning on chalcogen perovskties/Final dataset for chalcogen perovskties.csv').drop('Unnamed: 0',axis=1)

data

from sklearn.preprocessing import OneHotEncoder

encoder = OneHotEncoder(sparse_output=False)
protos=encoder.fit_transform(data[['Prototype']])
data[encoder.get_feature_names_out()]=protos

data

prot_counts=(data.Prototype.value_counts())

new_df = data[~data['Prototype'].isin((prot_counts.loc[prot_counts<10].index))]
new_df


from lazypredict.Supervised import LazyClassifier, LazyRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error
# Select numerical features
fin_data = new_df.select_dtypes(np.number)

# Split data into features and target
X = fin_data.drop('Bandgap', axis=1)  # Assuming 'Bandgap' is the target variable
y = fin_data['Bandgap']

# Split data into train and test sets
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.1, random_state=42)

# Initialize LazyPredict classifier and regressor with custom MAE metric
def mae_score(y_true, y_pred):
    return mean_absolute_error(y_true, y_pred)

reg = LazyRegressor(verbose=0, ignore_warnings=True, custom_metric=mae_score)

# Fit and get model performance report
models_train, predictions_train = reg.fit(X_train, X_test, y_train, y_test)

# Display results
print("\nModel Performance Ranking:")
print(models_train)


from lazypredict.Supervised import LazyClassifier, LazyRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error
# Select numerical features
fin_data = new_df.select_dtypes(np.number)

# Split data into features and target
X = fin_data.drop('Bandgap', axis=1)  # Assuming 'Bandgap' is the target variable
y = fin_data['Bandgap']>0

# Split data into train and test sets
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.1, random_state=42)

# Initialize LazyPredict classifier and regressor with custom MAE metric

classifier = LazyClassifier(verbose=0, ignore_warnings=True, custom_metric=None)

# Fit and get model performance report
models_train, predictions_train = classifier.fit(X_train, X_test, y_train, y_test)

# Display results
print("\nModel Performance Ranking:")
print(models_train)
