from pymatgen.io.vasp.outputs import Vasprun
vp = Vasprun('/home/<USER>/Coding/easy/primitive/vasprun.xml')

import pandas as pd
lv=0.05
uv=1.99
data=pd.read_csv(r'/home/<USER>/Coding/easy/without_mask.csv')
data=data.iloc[:,1:]         #Drops the first column (likely an index or unnecessary column)
data.columns=['kdist','energy','size']   #Renames columns for clarity (k-point distance, energy at that k-point)
for_vasp=data[['energy']].to_numpy().reshape(197,192,1)
#Converts selected columns to a NumPy array and reshapes it into a 3D array with dimensions
bands=data['kdist'].unique()

kpath_df = pd.read_csv('/home/<USER>/Coding/easy/primitive/KPOINTS_band', 
                 skiprows=3,  # Skip header lines
                 delim_whitespace=True,  # Handle variable whitespace
                 header=None,  # No header row
                 engine='python')  # More flexible engine
vp.actual_kpoints=kpath_df.iloc[:,[0,1,2]].values

replace_key=list(vp.eigenvalues.keys())[0]
vp.eigenvalues[replace_key]=for_vasp
 


from ifermi.interpolate import FourierInterpolator
bs=vp.get_band_structure()


%matplotlib inline


from pymatgen.electronic_structure.plotter import BSPlotter

from ifermi.interpolate import FourierInterpolator,LinearInterpolator

interpolator = FourierInterpolator(bs)
dense_bs, velocities = interpolator.interpolate_bands(interpolation_factor=0.005, return_velocities=True)


import numpy as np

kpoints = dense_bs.kpoints
kpoint_coords = np.array([k.frac_coords for k in kpoints])
print(kpoint_coords.shape)

for_vasp.shape,kpoint_coords.shape

Linterpolator=LinearInterpolator(kpoint_coords,{'<Spin.up: 1>':for_vasp})
linear_dense_bs=Linterpolator.interpolate(16,16,16)

from ifermi.surface import FermiSurface
from ifermi.kpoints import kpoints_from_bandstructure

dense_kpoints = kpoints_from_bandstructure(dense_bs)

fs = FermiSurface.from_band_structure(
    dense_bs,
    mu=0.0,
    property_data=velocities,
    property_kpoints=dense_kpoints,
    wigner_seitz=True
)

linterpolate=LinearInterpolator(fs)

from ifermi.plot import FermiSurfacePlotter
from pymatgen.electronic_structure.core import Spin
plotter = FermiSurfacePlotter(fs)


fs.isosurfaces

kpoints = dense_bs.kpoints
kpoint_coords = np.array([k.frac_coords for k in kpoints])


plot = plotter.get_plot(plot_index={Spin.up: [174,175,176,177]})

plot.show()




plot = plotter.get_plot()

plot.show()

import numpy as np
from pymatgen.electronic_structure.core import Spin

def write_bxsf(filename, eigenvalues, kgrid, fermi_energy, reciprocal_vectors):
    """Write eigenvalues to a .bxsf file for visualization in XCrySDen."""
    with open(filename, 'w') as f:
        f.write("BEGIN_INFO\n")
        f.write(f" Fermi Energy: {fermi_energy:.5f}\n")
        f.write("END_INFO\n\n")
        
        f.write("BEGIN_BLOCK_BANDGRID_3D\n")
        f.write(" Fermi_Surface\n")
        f.write(" BEGIN_BANDGRID_3D\n")
        f.write(f" {len(eigenvalues)}\n")  # Number of bands crossing Fermi level
        f.write(f" {kgrid[0]} {kgrid[1]} {kgrid[2]}\n")  # k-point grid
        f.write(" 0 0 0\n")  # Origin (Gamma point)
        
        # Write reciprocal lattice vectors
        for vector in reciprocal_vectors:
            f.write(f" {vector[0]} {vector[1]} {vector[2]}\n")
        
        # Write eigenvalues for each band
        for i, band in enumerate(eigenvalues):
            f.write(f" BAND: {i+1}\n")
            for kz in range(kgrid[2]):
                for ky in range(kgrid[1]):
                    for kx in range(kgrid[0]):
                        f.write(f" {band[kx][ky][kz]}")
                    f.write("\n")
        
        f.write(" END_BANDGRID_3D\n")
        f.write("END_BLOCK_BANDGRID_3D\n")

# Save the band structure data to a .bxsf file
# Extract data from the band structure object
reciprocal_vectors = dense_bs.structure.lattice.reciprocal_lattice.matrix
fermi_energy = dense_bs.efermi

# Get the bands near the Fermi level
energy_range = 2.0  # eV above and below Fermi level
band_indices = []

for i in range(dense_bs.nb_bands):
    band_energies = dense_bs.bands[Spin.up][i]
    min_energy = min(band_energies)
    max_energy = max(band_energies)
    
    # Check if this band crosses or is near the Fermi level
    if min_energy - energy_range <= fermi_energy <= max_energy + energy_range:
        band_indices.append(i)

# Get the k-point grid dimensions
kpoints = dense_bs.kpoints
kpoint_coords = np.array([k.frac_coords for k in kpoints])

# Determine the grid dimensions
unique_kx = np.unique(kpoint_coords[:, 0])
unique_ky = np.unique(kpoint_coords[:, 1])
unique_kz = np.unique(kpoint_coords[:, 2])
kgrid = (len(unique_kx), len(unique_ky), len(unique_kz))

# Extract eigenvalues for each band
eigenvalues = []
spin = Spin.up  # Use up spin if spin-polarized, otherwise use the only spin channel

for band_idx in band_indices:
    # Create a 3D grid for this band's eigenvalues
    band_eigenvalues = np.zeros(kgrid)
    
    # Fill the grid with eigenvalues
    for i, k in enumerate(kpoints):
        # Find the indices in the grid
        kx_idx = np.where(unique_kx == kpoint_coords[i, 0])[0][0]
        ky_idx = np.where(unique_ky == kpoint_coords[i, 1])[0][0]
        kz_idx = np.where(unique_kz == kpoint_coords[i, 2])[0][0]
        
        # Get the eigenvalue for this k-point and band
        eigenvalue = dense_bs.bands[spin][band_idx, i]
        
        # Store in the grid
        band_eigenvalues[kx_idx, ky_idx, kz_idx] = eigenvalue
    
    eigenvalues.append(band_eigenvalues)

# Write to .bxsf file
output_file = "file.bxsf"
write_bxsf(output_file, eigenvalues, kgrid, fermi_energy, reciprocal_vectors)
print(f"Band structure saved to {output_file} with {len(band_indices)} bands")


vp.eigenvalues.values()