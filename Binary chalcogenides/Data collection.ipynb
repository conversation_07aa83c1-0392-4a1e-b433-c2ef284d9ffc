import tensorflow as tf
print("TensorFlow version:", tf.__version__)
print("\nNum GPUs Available: ", len(tf.config.list_physical_devices('GPU')))
print("\nGPU Devices:", tf.config.list_physical_devices('GPU'))
print("\nCUDA available:", tf.test.is_built_with_cuda())
print("Is GPU available:", tf.test.is_built_with_gpu_support())

# Configure GPU memory growth
gpus = tf.config.list_physical_devices('GPU')
if gpus:
    try:
        # Currently, memory growth needs to be the same across GPUs
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        logical_gpus = tf.config.list_logical_devices('GPU')
        print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPUs")
    except RuntimeError as e:
        # Memory growth must be set before GPUs have been initialized
        print(e)

api='h79cBkPf4hpT24odYyHNpGTRNWuuPh2E'

import tensorflow as tf
print("TensorFlow version:", tf.__version__)
print("\nGPU devices:", tf.config.list_physical_devices('GPU'))
print("\nCUDA setup:")
import subprocess
subprocess.run(['nvidia-smi'], capture_output=True, text=True).stdout

conda install tensorflow

	import tensorflow as tf
	import numpy as np
	 
	X = np.array([-7.0, -4.0, -1.0, 2.0, 5.0, 8.0, 11.0, 14.0])
	y = np.array([3.0, 6.0, 9.0, 12.0, 15.0, 18.0, 21.0, 24.0])
	 
	tf.random.set_seed(42)
	 
	model = tf.keras.Sequential([
	  tf.keras.layers.Dense(1)
	])
	 
	model.compile(loss=tf.keras.losses.mae,
	              optimizer=tf.keras.optimizers.SGD(),
	              metrics=["mae"])
	 
	model.fit(tf.expand_dims(X, axis=-1), y, epochs=5)



'''from optimade.client import OptimadeClient
client = OptimadeClient()
results = client.get('elements HAS "Ag"')'''

metals=[    'Sc', 'Ti', 'V', 'Cr', 'Mn', 'Fe', 'Co', 'Ni', 
    'Cu', 'Zn', 'Y', 'Zr', 'Nb', 'Mo', 'Tc', 'Ru',
    'Rh', 'Pd', 'Ag', 'Cd', 'Hf', 'Ta', 'W', 'Re',
    'Os', 'Ir', 'Pt', 'Au', 'Hg']
chalcogens=['S','Se','Te']


chemsys=[]
for metal in metals:
        for chalc in chalcogens:
                chemsys.append({f'{metal}-{chalc}'})
len(chemsys)

from mp_api.client import MPRester

docs=[]
with MPRester(api) as mpr:
    for chem in chemsys:
        doc=mpr.materials.summary.search(
            chemsys=chem,
            formula='AB2',
            fields=['formula_pretty','material_id','band_gap','symmetry','structure','warnings']
        )
        list_of_available_fields = mpr.materials.summary.available_fields
        docs.append(doc)

fin_docs=[]
for doc in docs:
    fin_docs+=doc

for doc in fin_docs:
    print(doc.formula_pretty)

docs

docs[100]



list_of_available_fields

docs



